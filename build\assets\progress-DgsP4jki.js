import{j as v,o as l}from"./index-BJe_fyUy.js";import{c as $,P as f}from"./button-DAovvefP.js";import{c as I}from"./index-xZ9hcdpf.js";var u="Progress",d=100,[E,D]=$(u),[j,R]=E(u),m=v.forwardRef((r,e)=>{const{__scopeProgress:n,value:o=null,max:a,getValueLabel:N=_,...b}=r;(a||a===0)&&!p(a)&&console.error(w(`${a}`,"Progress"));const t=p(a)?a:d;o!==null&&!c(o,t)&&console.error(y(`${o}`,"Progress"));const s=c(o,t)?o:null,h=i(s)?N(s,t):void 0;return l.jsx(j,{scope:n,value:s,max:t,children:l.jsx(f.div,{"aria-valuemax":t,"aria-valuemin":0,"aria-valuenow":i(s)?s:void 0,"aria-valuetext":h,role:"progressbar","data-state":P(s,t),"data-value":s??void 0,"data-max":t,...b,ref:e})})});m.displayName=u;var g="ProgressIndicator",x=v.forwardRef((r,e)=>{const{__scopeProgress:n,...o}=r,a=R(g,n);return l.jsx(f.div,{"data-state":P(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...o,ref:e})});x.displayName=g;function _(r,e){return`${Math.round(r/e*100)}%`}function P(r,e){return r==null?"indeterminate":r===e?"complete":"loading"}function i(r){return typeof r=="number"}function p(r){return i(r)&&!isNaN(r)&&r>0}function c(r,e){return i(r)&&!isNaN(r)&&r<=e&&r>=0}function w(r,e){return`Invalid prop \`max\` of value \`${r}\` supplied to \`${e}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${d}\`.`}function y(r,e){return`Invalid prop \`value\` of value \`${r}\` supplied to \`${e}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${d} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var M=m,V=x;function L({className:r,value:e,...n}){return l.jsx(M,{"data-slot":"progress",className:I("bg-primary/20 relative h-1.5 w-full overflow-hidden rounded-full",r),...n,children:l.jsx(V,{"data-slot":"progress-indicator",className:"bg-pink-400 h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(e||0)}%)`}})})}export{L as P};
