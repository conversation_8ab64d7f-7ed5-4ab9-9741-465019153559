import { TransitionGroup, CSSTransition } from 'react-transition-group';
import { useLocation } from 'react-router';
import { useRef } from 'react';

export default function AnimatedRoutes({children}) {
  const location = useLocation();
  const nodeRef = useRef(null);

  return (
    <TransitionGroup component={null}>
      <CSSTransition
        key={location.pathname}
        nodeRef={nodeRef}
        timeout={300}
        classNames="page"
        unmountOnExit
      >
        <div
          ref={nodeRef}
          className="page w-full h-full"
        >
          {children}
        </div>
      </CSSTransition>
    </TransitionGroup>
  );
}