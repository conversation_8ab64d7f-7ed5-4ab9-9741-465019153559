import{j as l,o as s,G as o}from"./index-BJe_fyUy.js";import{P as m}from"./button-DAovvefP.js";import{c as p}from"./index-xZ9hcdpf.js";import{g as f}from"./index-DR-yOLsH.js";var h="Separator",i="horizontal",v=["horizontal","vertical"],d=l.forwardRef((t,e)=>{const{decorative:a,orientation:r=i,...c}=t,n=S(r)?r:i,u=a?{role:"none"}:{"aria-orientation":n==="vertical"?n:void 0,role:"separator"};return s.jsx(m.div,{"data-orientation":n,...u,...c,ref:e})});d.displayName=h;function S(t){return v.includes(t)}var B=d;function N({className:t,orientation:e="horizontal",decorative:a=!0,...r}){return s.jsx(B,{"data-slot":"separator",decorative:a,orientation:e,className:p("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...r})}const w=()=>{const t=f(),e=o(r=>r.sidebarConfig);return{sidebarTeams:()=>e.teams,sidebarNavItems:t}},O=()=>{const t=o(r=>r.setCurrentBread);return{currentBread:o(r=>r.breadConfig.currentBread),handleSetCurrentBread:r=>{r&&(r=r.slice(1),t(r.split("/")))}}},E=()=>{const t=o(a=>a.settingBarConfig.showHeaderTab),e=o(a=>a.setShowHeaderTab);return{showHeaderTab:t,setShowHeaderTab:e}};export{N as S,E as a,w as b,O as u};
