import{o as l}from"./index-BJe_fyUy.js";import{B as r}from"./button-DAovvefP.js";import{C as t}from"./checkbox-NImTB0VJ.js";import{D as i,a as c,b as o,c as n,d as u}from"./dropdown-menu-ew1rtEhD.js";import{B as d}from"./badge-DqICxM07.js";import{a as x}from"./index-xZ9hcdpf.js";import{E as p}from"./ellipsis-DKAoBi0p.js";import{M as j}from"./move-down-BZA86yqv.js";import{c as C}from"./index-CzvqTnBS.js";const a=C();function f({handleOpenEditDialog:h,handleDelete:g}){return[a.display({id:"select",header:({table:e})=>l.jsx(t,{checked:e.getIsAllPageRowsSelected()?!0:e.getIsSomePageRowsSelected()?"indeterminate":!1,onCheckedChange:s=>e.toggleAllPageRowsSelected(!!s),"aria-label":"Select all"}),cell:({row:e})=>l.jsx(t,{checked:e.getIsSelected(),onCheckedChange:s=>e.toggleSelected(!!s),"aria-label":"Select row"}),enableSorting:!1,enableHiding:!1}),a.accessor("id",{header:"ID"}),a.accessor("name",{header:"名称",cell:e=>e.getValue()}),a.accessor("label",{header:"标签",cell:e=>e.getValue()}),a.accessor("status",{header:"状态",cell:e=>l.jsx(d,{className:x(e.getValue()===0?"bg-destructive":"bg-primary"),children:e.getValue()===1?"开启":"关闭"})}),a.accessor("order",{header:"排序",cell:e=>l.jsx(d,{children:e.getValue()})}),a.accessor("desc",{header:"描述",cell:e=>l.jsx("pre",{children:e.getValue()})}),a.display({id:"actions",header:({table:e})=>l.jsx("div",{className:" flex",children:l.jsxs(i,{children:[l.jsx(c,{asChild:!0,children:l.jsxs(r,{variant:"ghost",className:"ml-auto",children:["属性筛选",l.jsx(j,{})]})}),l.jsx(o,{align:"end",children:e.getAllColumns().filter(s=>s.getCanHide()).map(s=>l.jsx(u,{className:"capitalize",checked:s.getIsVisible(),onCheckedChange:m=>s.toggleVisibility(!!m),children:s.id},s.id))})]})}),cell:({row:e})=>l.jsx("div",{className:" flex justify-end",children:l.jsxs(i,{children:[l.jsx(c,{asChild:!0,children:l.jsx(r,{variant:"ghost",className:"h-8 p-0",children:l.jsx(p,{className:"h-4 w-4"})})}),l.jsxs(o,{align:"end",children:[l.jsx(n,{onClick:()=>h(e.original),children:"编辑"}),l.jsx(n,{className:"text-red-600",onClick:()=>g(e.original.id),children:"删除"})]})]})})})]}const V=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));export{f as C,V as _};
