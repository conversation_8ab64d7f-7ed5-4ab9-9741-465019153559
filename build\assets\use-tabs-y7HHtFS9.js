import{c as m}from"./index-DR-yOLsH.js";import{G as s}from"./index-BJe_fyUy.js";const g=()=>{const o=m(),r=s(e=>e.selectedTab),a=s(e=>e.savedTabs),T=s(e=>e.addTab),c=s(e=>e.removeTab),n=s(e=>e.setSelectedTab);return{savedTabs:a,removeTab:e=>{const t=a.findIndex(b=>b.meta.key===e);if(t===-1)return;if(e!==r){c(e);return}if(a.length<=1){c(e),o("/"),n("");return}let d;t>=a.length-1?d=a[t-1]:d=a[t+1],c(e),n(d.meta.key),o(d.meta.key)},selectedTab:r,setSelectedTab:n,navigateToTab:e=>{o(e)},addTab:e=>{if(a.find(t=>t.meta.key===e.meta.key)){n(e.meta.key);return}T(e),n(e.meta.key)}}};export{g as u};
