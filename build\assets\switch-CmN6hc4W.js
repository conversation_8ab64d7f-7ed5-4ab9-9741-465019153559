import{j as a,o as s}from"./index-BJe_fyUy.js";import{c as T}from"./index-Dc_FVRD7.js";import{u as C,c as g}from"./index-xZ9hcdpf.js";import{c as I,P as y}from"./button-DAovvefP.js";import{u as M}from"./index-o2xk-IHR.js";import{u as H}from"./index-DE29eaAY.js";import{u as z}from"./index-Cs49eo9X.js";var m="Switch",[A,Q]=I(m),[U,q]=A(m),P=a.forwardRef((e,r)=>{const{__scopeSwitch:t,name:n,checked:o,defaultChecked:b,required:d,disabled:c,value:u="on",onCheckedChange:w,form:i,...k}=e,[l,p]=a.useState(null),v=C(r,f=>p(f)),S=a.useRef(!1),x=l?i||!!l.closest("form"):!0,[h,N]=M({prop:o,defaultProp:b??!1,onChange:w,caller:m});return s.jsxs(U,{scope:t,checked:h,disabled:c,children:[s.jsx(y.button,{type:"button",role:"switch","aria-checked":h,"aria-required":d,"data-state":_(h),"data-disabled":c?"":void 0,disabled:c,value:u,...k,ref:v,onClick:T(e.onClick,f=>{N(B=>!B),x&&(S.current=f.isPropagationStopped(),S.current||f.stopPropagation())})}),x&&s.jsx(j,{control:l,bubbles:!S.current,name:n,value:u,checked:h,required:d,disabled:c,form:i,style:{transform:"translateX(-100%)"}})]})});P.displayName=m;var E="SwitchThumb",R=a.forwardRef((e,r)=>{const{__scopeSwitch:t,...n}=e,o=q(E,t);return s.jsx(y.span,{"data-state":_(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:r})});R.displayName=E;var L="SwitchBubbleInput",j=a.forwardRef(({__scopeSwitch:e,control:r,checked:t,bubbles:n=!0,...o},b)=>{const d=a.useRef(null),c=C(d,b),u=H(t),w=z(r);return a.useEffect(()=>{const i=d.current;if(!i)return;const k=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(k,"checked").set;if(u!==t&&p){const v=new Event("click",{bubbles:n});p.call(i,t),i.dispatchEvent(v)}},[u,t,n]),s.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...o,tabIndex:-1,ref:c,style:{...o.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});j.displayName=L;function _(e){return e?"checked":"unchecked"}var O=P,D=R;function V({className:e,...r}){return s.jsx(O,{"data-slot":"switch",className:g("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:s.jsx(D,{"data-slot":"switch-thumb",className:g("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}export{V as S};
