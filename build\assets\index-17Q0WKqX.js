import{j as d,o as e}from"./index-BJe_fyUy.js";import{S as b,A as S,a as y,b as T,B as w,c as N,d as I,e as _,f as L,g as B}from"./breadcrumb-CSshGtI_.js";import{S as P}from"./scroll-area-Chr-8LXw.js";import{S as A}from"./system-setting-_cAp0rJA.js";import{u as E,S as k}from"./use-system-CxkFT_A_.js";import{u as M,T as O,O as z}from"./index-DR-yOLsH.js";import{P as C}from"./progress-DgsP4jki.js";import{S as R}from"./system-tabs-H2apK73Y.js";function $(){const[r,s]=d.useState(0),[o,i]=d.useState(!1),c=M();return d.useEffect(()=>{let t,m;const f=()=>{i(!0),s(0);let l=0;const x=setInterval(()=>{if(l+=10,s(l),l>=30){clearInterval(x);const h=setInterval(()=>{l+=2,s(l),l>=70&&clearInterval(h)},50);t=h}},20);t=x},n=()=>{t&&clearInterval(t),s(100),m=setTimeout(()=>{s(0),i(!1)},200)};f();const u=setTimeout(()=>{n()},300+Math.random()*200);return()=>{t&&clearInterval(t),m&&clearTimeout(m),u&&clearTimeout(u)}},[c.pathname,c.hash]),o&&r>0?e.jsx("div",{className:"fixed bottom-0 left-0 right-0 z-[99999] w-screen opacity-80",children:e.jsx(C,{value:r,className:"h-[3px] shadow-2xl rounded-none"})}):null}var F=(r,s,o,i,c,t,m,f)=>{let n=document.documentElement,u=["light","dark"];function l(a){(Array.isArray(r)?r:[r]).forEach(p=>{let g=p==="class",v=g&&t?c.map(j=>t[j]||j):c;g?(n.classList.remove(...v),n.classList.add(t&&t[a]?t[a]:a)):n.setAttribute(p,a)}),x(a)}function x(a){f&&u.includes(a)&&(n.style.colorScheme=a)}function h(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}if(i)l(i);else try{let a=localStorage.getItem(s)||o,p=m&&a==="system"?h():a;l(p)}catch{}},H=d.createContext(void 0),J={setTheme:r=>{},themes:[]},K=()=>{var r;return(r=d.useContext(H))!=null?r:J};d.memo(({forcedTheme:r,storageKey:s,attribute:o,enableSystem:i,enableColorScheme:c,defaultTheme:t,value:m,themes:f,nonce:n,scriptProps:u})=>{let l=JSON.stringify([o,s,t,r,f,m,i,c]).slice(1,-1);return d.createElement("script",{...u,suppressHydrationWarning:!0,nonce:typeof window>"u"?n:"",dangerouslySetInnerHTML:{__html:`(${F.toString()})(${l})`}})});const U=({...r})=>{const{theme:s="system"}=K();return e.jsx(O,{theme:s,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...r})};function W(){const{currentBread:r}=E();return e.jsxs("div",{className:"h-full w-full flex flex-col",children:[e.jsx($,{}),e.jsxs(b,{children:[e.jsx(S,{}),e.jsxs(y,{className:"flex flex-col h-full",children:[e.jsxs("header",{className:"flex felx-row flex-wrap border-b-2 border-primary ",children:[e.jsxs("div",{className:"flex justify-between h-12 items-center gap-0 w-full z-10",children:[e.jsxs("div",{className:"flex items-center gap-2 px-2",children:[e.jsx(T,{className:"-ml-1"}),e.jsx(k,{orientation:"vertical",className:"mr-2 data-[orientation=vertical]:h-4"}),e.jsx(w,{children:e.jsx(N,{children:r&&r.length>0&&e.jsx(e.Fragment,{children:r.map((s,o)=>e.jsx(I,{children:o===r.length-1?e.jsx(_,{children:s}):e.jsxs(e.Fragment,{children:[e.jsx(L,{href:"#",children:s}),e.jsx(B,{})]})},o))})})})]}),e.jsx("div",{className:"flex items-center gap-2 px-0",children:e.jsx(A,{})})]}),e.jsx(R,{})]}),e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsx(P,{className:"h-full w-full",children:e.jsxs("div",{className:"flex flex-1 flex-col p-4",children:[e.jsx(U,{}),e.jsx(z,{})]})})})]})]})]})}const ee=Object.freeze(Object.defineProperty({__proto__:null,default:W},Symbol.toStringTag,{value:"Module"}));export{ee as _};
