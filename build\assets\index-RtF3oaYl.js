import{j as i,o as c}from"./index-BJe_fyUy.js";import{u as O,d as me}from"./index-xZ9hcdpf.js";import{P as h,d as ge,c as ve,a as Ee}from"./button-DAovvefP.js";import{u as _}from"./index-T-r5sLM7.js";import{u as De}from"./index-o2xk-IHR.js";import{u as z}from"./index-CoAOa-vU.js";import{u as ye,P as he,h as Oe,R as Ce,F as Pe}from"./index-UUrTRFO3.js";import{u as k}from"./index-CUmJ2nuE.js";function D(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}var Re="DismissableLayer",M="dismissableLayer.update",Ne="dismissableLayer.pointerDownOutside",xe="dismissableLayer.focusOutside",G,q=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),K=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:d,onDismiss:u,...f}=e,a=i.useContext(q),[l,v]=i.useState(null),m=l?.ownerDocument??globalThis?.document,[,E]=i.useState({}),A=O(t,p=>v(p)),C=Array.from(a.layers),[I]=[...a.layersWithOutsidePointerEventsDisabled].slice(-1),de=C.indexOf(I),j=l?C.indexOf(l):-1,fe=a.layersWithOutsidePointerEventsDisabled.size>0,W=j>=de,pe=Ie(p=>{const P=p.target,B=[...a.branches].some(T=>T.contains(P));!W||B||(o?.(p),d?.(p),p.defaultPrevented||u?.())},m),U=Te(p=>{const P=p.target;[...a.branches].some(T=>T.contains(P))||(s?.(p),d?.(p),p.defaultPrevented||u?.())},m);return ye(p=>{j===a.layers.size-1&&(r?.(p),!p.defaultPrevented&&u&&(p.preventDefault(),u()))},m),i.useEffect(()=>{if(l)return n&&(a.layersWithOutsidePointerEventsDisabled.size===0&&(G=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),a.layersWithOutsidePointerEventsDisabled.add(l)),a.layers.add(l),$(),()=>{n&&a.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=G)}},[l,m,n,a]),i.useEffect(()=>()=>{l&&(a.layers.delete(l),a.layersWithOutsidePointerEventsDisabled.delete(l),$())},[l,a]),i.useEffect(()=>{const p=()=>E({});return document.addEventListener(M,p),()=>document.removeEventListener(M,p)},[]),c.jsx(h.div,{...f,ref:A,style:{pointerEvents:fe?W?"auto":"none":void 0,...e.style},onFocusCapture:D(e.onFocusCapture,U.onFocusCapture),onBlurCapture:D(e.onBlurCapture,U.onBlurCapture),onPointerDownCapture:D(e.onPointerDownCapture,pe.onPointerDownCapture)})});K.displayName=Re;var be="DismissableLayerBranch",Ae=i.forwardRef((e,t)=>{const n=i.useContext(q),r=i.useRef(null),o=O(t,r);return i.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),c.jsx(h.div,{...e,ref:o})});Ae.displayName=be;function Ie(e,t=globalThis?.document){const n=z(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const s=u=>{if(u.target&&!r.current){let f=function(){V(Ne,n,a,{discrete:!0})};const a={originalEvent:u};u.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=f,t.addEventListener("click",o.current,{once:!0})):f()}else t.removeEventListener("click",o.current);r.current=!1},d=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(d),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Te(e,t=globalThis?.document){const n=z(e),r=i.useRef(!1);return i.useEffect(()=>{const o=s=>{s.target&&!r.current&&V(xe,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function $(){const e=new CustomEvent(M);document.dispatchEvent(e)}function V(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?ge(o,s):o.dispatchEvent(s)}function _e(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var x=e=>{const{present:t,children:n}=e,r=we(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),s=O(r.ref,Me(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:s}):null};x.displayName="Presence";function we(e){const[t,n]=i.useState(),r=i.useRef(null),o=i.useRef(e),s=i.useRef("none"),d=e?"mounted":"unmounted",[u,f]=_e(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const a=R(r.current);s.current=u==="mounted"?a:"none"},[u]),k(()=>{const a=r.current,l=o.current;if(l!==e){const m=s.current,E=R(a);e?f("MOUNT"):E==="none"||a?.display==="none"?f("UNMOUNT"):f(l&&m!==E?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,f]),k(()=>{if(t){let a;const l=t.ownerDocument.defaultView??window,v=E=>{const C=R(r.current).includes(CSS.escape(E.animationName));if(E.target===t&&C&&(f("ANIMATION_END"),!o.current)){const I=t.style.animationFillMode;t.style.animationFillMode="forwards",a=l.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=I)})}},m=E=>{E.target===t&&(s.current=R(r.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",v),t.addEventListener("animationend",v),()=>{l.clearTimeout(a),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",v),t.removeEventListener("animationend",v)}}else f("ANIMATION_END")},[t,f]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:i.useCallback(a=>{r.current=a?getComputedStyle(a):null,n(a)},[])}}function R(e){return e?.animationName||"none"}function Me(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var w=0;function Se(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??H()),document.body.insertAdjacentElement("beforeend",e[1]??H()),w++,()=>{w===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),w--}},[])}function H(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var b="Dialog",[Y,Je]=ve(b),[Le,g]=Y(b),X=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:d=!0}=e,u=i.useRef(null),f=i.useRef(null),[a,l]=De({prop:r,defaultProp:o??!1,onChange:s,caller:b});return c.jsx(Le,{scope:t,triggerRef:u,contentRef:f,contentId:_(),titleId:_(),descriptionId:_(),open:a,onOpenChange:l,onOpenToggle:i.useCallback(()=>l(v=>!v),[l]),modal:d,children:n})};X.displayName=b;var Z="DialogTrigger",J=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=g(Z,n),s=O(t,o.triggerRef);return c.jsx(h.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":F(o.open),...r,ref:s,onClick:D(e.onClick,o.onOpenToggle)})});J.displayName=Z;var S="DialogPortal",[Fe,Q]=Y(S,{forceMount:void 0}),ee=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=g(S,t);return c.jsx(Fe,{scope:t,forceMount:n,children:i.Children.map(r,d=>c.jsx(x,{present:n||s.open,children:c.jsx(he,{asChild:!0,container:o,children:d})}))})};ee.displayName=S;var N="DialogOverlay",te=i.forwardRef((e,t)=>{const n=Q(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=g(N,e.__scopeDialog);return s.modal?c.jsx(x,{present:r||s.open,children:c.jsx(We,{...o,ref:t})}):null});te.displayName=N;var je=me("DialogOverlay.RemoveScroll"),We=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=g(N,n);return c.jsx(Ce,{as:je,allowPinchZoom:!0,shards:[o.contentRef],children:c.jsx(h.div,{"data-state":F(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),y="DialogContent",ne=i.forwardRef((e,t)=>{const n=Q(y,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=g(y,e.__scopeDialog);return c.jsx(x,{present:r||s.open,children:s.modal?c.jsx(Ue,{...o,ref:t}):c.jsx(Be,{...o,ref:t})})});ne.displayName=y;var Ue=i.forwardRef((e,t)=>{const n=g(y,e.__scopeDialog),r=i.useRef(null),o=O(t,n.contentRef,r);return i.useEffect(()=>{const s=r.current;if(s)return Oe(s)},[]),c.jsx(oe,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:D(e.onCloseAutoFocus,s=>{s.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:D(e.onPointerDownOutside,s=>{const d=s.detail.originalEvent,u=d.button===0&&d.ctrlKey===!0;(d.button===2||u)&&s.preventDefault()}),onFocusOutside:D(e.onFocusOutside,s=>s.preventDefault())})}),Be=i.forwardRef((e,t)=>{const n=g(y,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return c.jsx(oe,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||n.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const d=s.target;n.triggerRef.current?.contains(d)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),oe=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...d}=e,u=g(y,n),f=i.useRef(null),a=O(t,f);return Se(),c.jsxs(c.Fragment,{children:[c.jsx(Pe,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:c.jsx(K,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":F(u.open),...d,ref:a,onDismiss:()=>u.onOpenChange(!1)})}),c.jsxs(c.Fragment,{children:[c.jsx(ke,{titleId:u.titleId}),c.jsx($e,{contentRef:f,descriptionId:u.descriptionId})]})]})}),L="DialogTitle",re=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=g(L,n);return c.jsx(h.h2,{id:o.titleId,...r,ref:t})});re.displayName=L;var se="DialogDescription",ie=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=g(se,n);return c.jsx(h.p,{id:o.descriptionId,...r,ref:t})});ie.displayName=se;var ae="DialogClose",ce=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=g(ae,n);return c.jsx(h.button,{type:"button",...r,ref:t,onClick:D(e.onClick,()=>o.onOpenChange(!1))})});ce.displayName=ae;function F(e){return e?"open":"closed"}var ue="DialogTitleWarning",[Qe,le]=Ee(ue,{contentName:y,titleName:L,docsSlug:"dialog"}),ke=({titleId:e})=>{const t=le(ue),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Ge="DialogDescriptionWarning",$e=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${le(Ge).contentName}}.`;return i.useEffect(()=>{const o=e.current?.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},et=X,tt=J,nt=ee,ot=te,rt=ne,st=re,it=ie,at=ce;export{rt as C,it as D,ot as O,nt as P,et as R,tt as T,st as a,at as b,ie as c};
