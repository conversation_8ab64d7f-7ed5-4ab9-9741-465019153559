import{j as i}from"./index-BJe_fyUy.js";import{u as y}from"./index-xZ9hcdpf.js";import{u as A}from"./index-CUmJ2nuE.js";function E(n,e){return i.useReducer((t,r)=>e[t][r]??t,n)}var T=n=>{const{present:e,children:t}=n,r=P(e),a=typeof t=="function"?t({present:r.isPresent}):i.Children.only(t),c=y(r.ref,R(a));return typeof t=="function"||r.isPresent?i.cloneElement(a,{ref:c}):null};T.displayName="Presence";function P(n){const[e,t]=i.useState(),r=i.useRef(null),a=i.useRef(n),c=i.useRef("none"),p=n?"mounted":"unmounted",[N,s]=E(p,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const o=l(r.current);c.current=N==="mounted"?o:"none"},[N]),A(()=>{const o=r.current,m=a.current;if(m!==n){const f=c.current,u=l(o);n?s("MOUNT"):u==="none"||o?.display==="none"?s("UNMOUNT"):s(m&&f!==u?"ANIMATION_OUT":"UNMOUNT"),a.current=n}},[n,s]),A(()=>{if(e){let o;const m=e.ownerDocument.defaultView??window,d=u=>{const g=l(r.current).includes(u.animationName);if(u.target===e&&g&&(s("ANIMATION_END"),!a.current)){const O=e.style.animationFillMode;e.style.animationFillMode="forwards",o=m.setTimeout(()=>{e.style.animationFillMode==="forwards"&&(e.style.animationFillMode=O)})}},f=u=>{u.target===e&&(c.current=l(r.current))};return e.addEventListener("animationstart",f),e.addEventListener("animationcancel",d),e.addEventListener("animationend",d),()=>{m.clearTimeout(o),e.removeEventListener("animationstart",f),e.removeEventListener("animationcancel",d),e.removeEventListener("animationend",d)}}else s("ANIMATION_END")},[e,s]),{isPresent:["mounted","unmountSuspended"].includes(N),ref:i.useCallback(o=>{r.current=o?getComputedStyle(o):null,t(o)},[])}}function l(n){return n?.animationName||"none"}function R(n){let e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get,t=e&&"isReactWarning"in e&&e.isReactWarning;return t?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get,t=e&&"isReactWarning"in e&&e.isReactWarning,t?n.props.ref:n.props.ref||n.ref)}export{T as P};
