import{o as n}from"./index-BJe_fyUy.js";import{a as s}from"./index-xZ9hcdpf.js";import{I as h}from"./index-DR-yOLsH.js";function p({icon:r,size:t="1em",color:o="currentColor",className:i="",style:l={},...a}){if(r.startsWith("url:")){const e=r.replace("url:","");return n.jsx("img",{src:e,alt:"icon",className:s("inline-block",i),style:{width:t,height:t,color:o,...l}})}return n.jsx(h,{icon:r,width:t,height:t,className:s("inline-block",i),style:{color:o,height:t,width:t,...l},...a})}export{p as I};
